// ========== TIPOS DE AUDIO ==========
export type AudioType = "music" | "speech" | "sfx";

export interface AudioState {
  music: {
    isPlaying: boolean;
    volume: number;
    currentTrack: string | null;
  };
  speech: {
    isPlaying: boolean;
    volume: number;
  };
  sfx: {
    volume: number;
  };
  masterVolume: number;
  isMuted: boolean;
}

// ========== TIPOS DE SPEECH Y VOZ ==========
export type VoiceGender = "male" | "female";
export type MessageType = "system" | "question" | "answer" | "hint" | "victory" | "error" | "guess";
export type PlaybackState = "idle" | "playing" | "paused" | "loading" | "error";

export type SpeechPriority = "critical" | "high" | "medium" | "low";
export type SpeechChannel = "web" | "auto";
export type SpeechType =
  | "error"
  | "validation"
  | "game_response"
  | "victory"
  | "instruction"
  | "question"
  | "welcome"
  | "hint"
  | "info"
  | "effect"
  | "character";

// ========== INTERFACES DE SPEECH ==========
export interface SpeechRequest {
  id: string;
  text: string;
  priority: SpeechPriority;
  type: SpeechType;
  channel: SpeechChannel;
  timestamp: number;
  timeout?: number;
  onStart?: () => void;
  onComplete?: () => void;
  onError?: (error: Error) => void;
}

export interface SpeechOutputState {
  isReady: boolean;
  isConfiguring: boolean;
  playbackState: PlaybackState;
  currentVoice: string;
  availableVoices: string[];
  errorMessage: string | null;
  audioState: AudioState;
  isMusicPlaying: boolean;
  isSpeechPlaying: boolean;
}

export interface SpeechState {
  isPlaying: boolean;
  currentRequest: SpeechRequest | null;
  queue: SpeechRequest[];
  channel: SpeechChannel;
  lastActivity: number;
}

export interface SpeechMessage {
  id: string;
  text: string;
  type: MessageType;
  timestamp: Date;
  duration?: number;
  voice?: string;
}
